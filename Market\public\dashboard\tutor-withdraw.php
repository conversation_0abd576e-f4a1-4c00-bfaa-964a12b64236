<?php
/**
 * Withdraw Page for Market Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Tutor LMS fonksiyonlarının mevcut olup olmadığını kontrol et
if (!function_exists('tutor') || !function_exists('tutor_utils')) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Tutor LMS eklentisi aktif değil. Para çekme işlemleri için Tutor LMS eklentisini aktifleştirin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Kullanıcının instructor olup olma<PERSON><PERSON><PERSON><PERSON> kontrol et
if (!current_user_can(tutor()->instructor_role)) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Para çekme işlemleri için instructor yetkisine sahip olmanız gerekiyor.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

use TUTOR\Input;
use Tutor\Models\WithdrawModel;

$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

$per_page = tutor_utils()->get_option('pagination_per_page', 20);
$current_page = max(1, Input::get('current_page', 1, Input::TYPE_INT));
$offset = ($current_page - 1) * $per_page;

$min_withdraw = tutor_utils()->get_option('min_withdraw_amount');
$formatted_min_withdraw_amount = tutor_utils()->tutor_price($min_withdraw);

$saved_account = WithdrawModel::get_user_withdraw_method($current_user_id);
$withdraw_method_name = tutor_utils()->avalue_dot('withdraw_method_name', $saved_account);

$withdraw_status = array(WithdrawModel::STATUS_PENDING, WithdrawModel::STATUS_APPROVED, WithdrawModel::STATUS_REJECTED);
$all_histories = WithdrawModel::get_withdrawals_history($current_user_id, array('status' => $withdraw_status), $offset, $per_page);

$method_icons = array(
    'bank_transfer_withdraw' => 'ni ni-building',
    'echeck_withdraw'        => 'ni ni-check-circle',
    'paypal_withdraw'        => 'ni ni-paypal',
);

$status_message = array(
    'rejected' => __('Lütfen daha fazla bilgi için site yöneticisi ile iletişime geçin.', 'marketking-multivendor-marketplace-for-woocommerce'),
    'pending'  => __('Para çekme talebiniz onay bekliyor, lütfen bekleyin.', 'marketking-multivendor-marketplace-for-woocommerce'),
);

$currency_symbol = '';
if (function_exists('get_woocommerce_currency_symbol')) {
    $currency_symbol = get_woocommerce_currency_symbol();
} elseif (function_exists('edd_currency_symbol')) {
    $currency_symbol = edd_currency_symbol();
}

$summary_data = WithdrawModel::get_withdraw_summary($current_user_id);
$available_for_withdraw = $summary_data->available_for_withdraw - $summary_data->total_pending;
$is_balance_sufficient = $available_for_withdraw >= $min_withdraw;
$available_for_withdraw_formatted = tutor_utils()->tutor_price($available_for_withdraw);
$current_balance_formated = tutor_utils()->tutor_price($summary_data->current_balance);

$dashboard_url = trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true)));
?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h4 class="nk-block-title page-title"><?php esc_html_e('Para Çekme', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                            <div class="nk-block-des text-soft">
                                <p><?php esc_html_e('Kazançlarınızı çekin ve para çekme geçmişinizi görüntüleyin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Balance Card -->
                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="row align-items-center">
                                <div class="col-lg-1">
                                    <div class="nk-block-icon">
                                        <em class="icon ni ni-wallet text-primary" style="font-size: 2rem;"></em>
                                    </div>
                                </div>
                                <div class="col-lg-8">
                                    <div class="nk-block-content">
                                        <h6 class="text-muted">
                                            <?php printf(esc_html__('Mevcut Bakiye %s', 'marketking-multivendor-marketplace-for-woocommerce'), $current_balance_formated); ?>
                                        </h6>
                                        <h5 class="title">
                                            <?php
                                            if ($is_balance_sufficient) {
                                                printf(esc_html__('Çekilebilir bakiyeniz %s', 'marketking-multivendor-marketplace-for-woocommerce'), "<strong class='text-success'>" . $available_for_withdraw_formatted . '</strong>');
                                            } else {
                                                printf(esc_html__('Bakiyeniz %s ve çekim için yetersiz', 'marketking-multivendor-marketplace-for-woocommerce'), "<strong class='text-warning'>" . $available_for_withdraw_formatted . '</strong>');
                                            }
                                            ?>
                                        </h5>
                                        <?php if ($summary_data->total_pending > 0) : ?>
                                            <div class="badge badge-warning mt-2">
                                                <?php printf(esc_html__('Bekleyen Çekim %s', 'marketking-multivendor-marketplace-for-woocommerce'), tutor_utils()->tutor_price($summary_data->total_pending)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="nk-block-content text-right">
                                        <?php if ($is_balance_sufficient && $withdraw_method_name) : ?>
                                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#tutor_earning_withdraw_modal">
                                                <em class="icon ni ni-wallet-out"></em>
                                                <span><?php esc_html_e('Para Çek', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                            </button>
                                        <?php elseif (!$withdraw_method_name) : ?>
                                            <a href="<?php echo esc_url($dashboard_url . 'profile-settings'); ?>" class="btn btn-outline-primary">
                                                <em class="icon ni ni-setting"></em>
                                                <span><?php esc_html_e('Ödeme Yöntemi Ekle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                            </a>
                                        <?php else : ?>
                                            <div class="text-muted">
                                                <?php printf(esc_html__('Minimum çekim tutarı: %s', 'marketking-multivendor-marketplace-for-woocommerce'), $formatted_min_withdraw_amount); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdraw History -->
                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <div class="card-inner position-relative card-tools-toggle">
                                <div class="card-title-group">
                                    <div class="card-tools">
                                        <div class="form-inline flex-nowrap gx-3">
                                            <div class="form-wrap w-150px">
                                                <select class="form-select" data-search="off" data-placeholder="<?php esc_attr_e('Durum', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                    <option value=""><?php esc_html_e('Tüm Durumlar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                    <option value="pending"><?php esc_html_e('Beklemede', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                    <option value="approved"><?php esc_html_e('Onaylandı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                    <option value="rejected"><?php esc_html_e('Reddedildi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-tools mr-n1">
                                        <ul class="btn-toolbar gx-1">
                                            <li>
                                                <a href="#" class="btn btn-icon search-toggle toggle-search" data-target="search">
                                                    <em class="icon ni ni-search"></em>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-search search-wrap" data-search="search">
                                    <div class="card-body">
                                        <div class="search-content">
                                            <a href="#" class="search-back btn btn-icon toggle-search" data-target="search">
                                                <em class="icon ni ni-arrow-left"></em>
                                            </a>
                                            <input type="text" class="form-control border-transparent form-focus-none" placeholder="<?php esc_attr_e('Tarih veya tutar ara...', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                            <button class="search-submit btn btn-icon">
                                                <em class="icon ni ni-search"></em>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (is_array($all_histories->results) && count($all_histories->results)) : ?>
                                <div class="card-inner p-0">
                                    <div class="nk-tb-list nk-tb-ulist">
                                        <div class="nk-tb-item nk-tb-head">
                                            <div class="nk-tb-col"><span class="sub-text"><?php esc_html_e('Tutar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Yöntem', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-lg"><span class="sub-text"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Durum', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col nk-tb-col-tools text-right">
                                                <div class="dropdown">
                                                    <a href="#" class="btn btn-xs btn-outline-light btn-icon dropdown-toggle" data-toggle="dropdown">
                                                        <em class="icon ni ni-plus"></em>
                                                    </a>
                                                    <div class="dropdown-menu dropdown-menu-right">
                                                        <ul class="link-list-opt no-bdr">
                                                            <?php if ($is_balance_sufficient && $withdraw_method_name) : ?>
                                                                <li><a href="#" data-toggle="modal" data-target="#tutor_earning_withdraw_modal">
                                                                    <em class="icon ni ni-wallet-out"></em><span><?php esc_html_e('Para Çek', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                </a></li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php foreach ($all_histories->results as $history) : ?>
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($history->status) {
                                                case 'pending':
                                                    $status_class = 'badge-warning';
                                                    $status_text = __('Beklemede', 'marketking-multivendor-marketplace-for-woocommerce');
                                                    break;
                                                case 'approved':
                                                    $status_class = 'badge-success';
                                                    $status_text = __('Onaylandı', 'marketking-multivendor-marketplace-for-woocommerce');
                                                    break;
                                                case 'rejected':
                                                    $status_class = 'badge-danger';
                                                    $status_text = __('Reddedildi', 'marketking-multivendor-marketplace-for-woocommerce');
                                                    break;
                                            }
                                            
                                            $method_data = maybe_unserialize($history->method_data);
                                            $method_name = isset($method_data['withdraw_method_name']) ? $method_data['withdraw_method_name'] : __('Bilinmiyor', 'marketking-multivendor-marketplace-for-woocommerce');
                                            $method_icon = isset($method_icons[$method_name]) ? $method_icons[$method_name] : 'ni ni-wallet';
                                            ?>
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col">
                                                    <div class="user-card">
                                                        <div class="user-info">
                                                            <span class="tb-amount"><?php echo wp_kses_post(tutor_utils()->tutor_price($history->amount)); ?></span>
                                                            <span class="fs-12px text-soft"><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($history->created_at))); ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col tb-col-md">
                                                    <span class="tb-status">
                                                        <em class="icon <?php echo esc_attr($method_icon); ?>"></em>
                                                        <?php echo esc_html($method_name); ?>
                                                    </span>
                                                </div>
                                                <div class="nk-tb-col tb-col-lg">
                                                    <span class="tb-date"><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($history->created_at))); ?></span>
                                                </div>
                                                <div class="nk-tb-col tb-col-md">
                                                    <span class="badge <?php echo esc_attr($status_class); ?>"><?php echo esc_html($status_text); ?></span>
                                                </div>
                                                <div class="nk-tb-col nk-tb-col-tools">
                                                    <ul class="nk-tb-actions gx-1">
                                                        <li>
                                                            <div class="drodown">
                                                                <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-toggle="dropdown">
                                                                    <em class="icon ni ni-more-h"></em>
                                                                </a>
                                                                <div class="dropdown-menu dropdown-menu-right">
                                                                    <ul class="link-list-opt no-bdr">
                                                                        <li><a href="#" class="view-withdraw-details" data-id="<?php echo esc_attr($history->id); ?>">
                                                                            <em class="icon ni ni-eye"></em><span><?php esc_html_e('Detayları Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                        </a></li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <!-- Pagination -->
                                <?php
                                $total_items = $all_histories->count;
                                if ($total_items > $per_page) {
                                    $total_pages = ceil($total_items / $per_page);
                                    if ($total_pages > 1) {
                                        ?>
                                        <div class="card-inner">
                                            <div class="nk-block-between-md g-3">
                                                <div class="g">
                                                    <ul class="pagination justify-content-center justify-content-md-start">
                                                        <?php
                                                        $current_url = $dashboard_url . 'tutor-withdraw';
                                                        
                                                        // Previous page
                                                        if ($current_page > 1) {
                                                            $prev_page = $current_page - 1;
                                                            $prev_url = $prev_page > 1 ? add_query_arg('current_page', $prev_page, $current_url) : $current_url;
                                                            ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="<?php echo esc_url($prev_url); ?>">
                                                                    <em class="icon ni ni-chevrons-left"></em>
                                                                </a>
                                                            </li>
                                                            <?php
                                                        }
                                                        
                                                        // Page numbers
                                                        for ($i = 1; $i <= $total_pages; $i++) {
                                                            $page_url = $i > 1 ? add_query_arg('current_page', $i, $current_url) : $current_url;
                                                            ?>
                                                            <li class="page-item<?php echo $i == $current_page ? ' active' : ''; ?>">
                                                                <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo esc_html($i); ?></a>
                                                            </li>
                                                            <?php
                                                        }
                                                        
                                                        // Next page
                                                        if ($current_page < $total_pages) {
                                                            $next_page = $current_page + 1;
                                                            $next_url = add_query_arg('current_page', $next_page, $current_url);
                                                            ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="<?php echo esc_url($next_url); ?>">
                                                                    <em class="icon ni ni-chevrons-right"></em>
                                                                </a>
                                                            </li>
                                                            <?php
                                                        }
                                                        ?>
                                                    </ul>
                                                </div>
                                                <div class="g">
                                                    <div class="pagination-goto d-flex justify-content-center justify-content-md-end gx-3">
                                                        <div><?php printf(esc_html__('Sayfa %1$d / %2$d', 'marketking-multivendor-marketplace-for-woocommerce'), $current_page, $total_pages); ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            <?php else : ?>
                                <div class="card-inner text-center">
                                    <div class="nk-empty-state">
                                        <div class="nk-empty-state-icon">
                                            <em class="icon ni ni-wallet"></em>
                                        </div>
                                        <h4 class="nk-empty-state-title"><?php esc_html_e('Henüz para çekme işlemi bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p class="nk-empty-state-text"><?php esc_html_e('İlk para çekme işleminizi yapmak için yeterli bakiyeniz olduğunda "Para Çek" butonuna tıklayın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                        <?php if ($is_balance_sufficient && $withdraw_method_name) : ?>
                                            <div class="nk-empty-state-action">
                                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#tutor_earning_withdraw_modal">
                                                    <em class="icon ni ni-wallet-out"></em>
                                                    <span><?php esc_html_e('Para Çek', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<?php if ($is_balance_sufficient && $withdraw_method_name) : ?>
<div class="modal fade" id="tutor_earning_withdraw_modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php esc_html_e('Para Çekme Talebi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" id="tutor_withdraw_form">
                <div class="modal-body">
                    <?php wp_nonce_field('tutor_withdraw_nonce', 'tutor_withdraw_nonce_field'); ?>
                    <input type="hidden" name="action" value="tutor_withdraw_request">
                    <input type="hidden" name="redirect_to" value="<?php echo esc_url($dashboard_url . 'tutor-withdraw'); ?>">
                    
                    <div class="form-group">
                        <label class="form-label"><?php esc_html_e('Çekilecek Tutar', 'marketking-multivendor-marketplace-for-woocommerce'); ?> <span class="text-danger">*</span></label>
                        <div class="form-control-wrap">
                            <input type="number" class="form-control" name="withdraw_amount" 
                                   min="<?php echo esc_attr($min_withdraw); ?>" 
                                   max="<?php echo esc_attr($available_for_withdraw); ?>" 
                                   step="0.01" required
                                   placeholder="<?php echo esc_attr($min_withdraw); ?>">
                            <div class="form-note">
                                <?php printf(esc_html__('Minimum: %1$s, Maksimum: %2$s', 'marketking-multivendor-marketplace-for-woocommerce'), $formatted_min_withdraw_amount, $available_for_withdraw_formatted); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label"><?php esc_html_e('Ödeme Yöntemi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <div class="form-control-wrap">
                            <input type="text" class="form-control" value="<?php echo esc_attr($withdraw_method_name); ?>" readonly>
                            <div class="form-note">
                                <a href="<?php echo esc_url($dashboard_url . 'profile-settings'); ?>"><?php esc_html_e('Ödeme yöntemini değiştir', 'marketking-multivendor-marketplace-for-woocommerce'); ?></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label"><?php esc_html_e('Not (İsteğe Bağlı)', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                        <textarea class="form-control" name="withdraw_note" rows="3" 
                                  placeholder="<?php esc_attr_e('Para çekme talebi ile ilgili notunuz...', 'marketking-multivendor-marketplace-for-woocommerce'); ?>"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-light" data-dismiss="modal"><?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?></button>
                    <button type="submit" class="btn btn-primary">
                        <em class="icon ni ni-wallet-out"></em>
                        <span><?php esc_html_e('Para Çekme Talebi Gönder', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
jQuery(document).ready(function($) {
    // View withdraw details
    $('.view-withdraw-details').on('click', function(e) {
        e.preventDefault();
        var withdrawId = $(this).data('id');
        
        // Here you can implement a modal or redirect to show withdraw details
        alert('<?php esc_html_e('Detay görüntüleme özelliği yakında eklenecek.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>');
    });
});
</script>
