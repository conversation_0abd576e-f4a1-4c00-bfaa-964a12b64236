<?php
/**
 * Create Course Page for Market Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Tutor LMS fonksiyonlarının mevcut olup olmadığını kontrol et
if (!function_exists('tutor') || !function_exists('tutor_utils')) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Tutor LMS eklentisi aktif değil. Kurs oluşturmak için Tutor LMS eklentisini aktifleştirin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Kullanıcının instructor olup olma<PERSON>ğ<PERSON><PERSON><PERSON> kontrol et
if (!current_user_can(tutor()->instructor_role)) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Kurs oluşturmak için instructor yetkisine sahip olmanız gerekiyor.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

use TUTOR\Input;

$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

$course_id = Input::get('course_id', 0, Input::TYPE_INT);
$dashboard_url = trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true)));

// Eğer course_id varsa, kursu düzenleme modunda
$is_edit_mode = $course_id > 0;
$page_title = $is_edit_mode ? __('Kursu Düzenle', 'marketking-multivendor-marketplace-for-woocommerce') : __('Yeni Kurs Oluştur', 'marketking-multivendor-marketplace-for-woocommerce');

if ($is_edit_mode) {
    $post = get_post($course_id);
    if (!$post || $post->post_type !== tutor()->course_post_type) {
        ?>
        <div class="nk-content">
            <div class="container-fluid">
                <div class="nk-content-inner">
                    <div class="nk-content-body">
                        <div class="alert alert-danger">
                            <?php esc_html_e('Kurs bulunamadı.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return;
    }
    
    // Kullanıcının bu kursu düzenleme yetkisi var mı kontrol et
    if ($post->post_author != $current_user_id && !current_user_can('administrator')) {
        ?>
        <div class="nk-content">
            <div class="container-fluid">
                <div class="nk-content-inner">
                    <div class="nk-content-body">
                        <div class="alert alert-danger">
                            <?php esc_html_e('Bu kursu düzenleme yetkiniz yok.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return;
    }
}
?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h4 class="nk-block-title page-title"><?php echo esc_html($page_title); ?></h4>
                            <div class="nk-block-des text-soft">
                                <p><?php echo $is_edit_mode ? esc_html__('Kursunuzu düzenleyin ve güncelleyin', 'marketking-multivendor-marketplace-for-woocommerce') : esc_html__('Yeni bir kurs oluşturun ve öğrencilerinizle paylaşın', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <a href="<?php echo esc_url($dashboard_url . 'my-courses'); ?>" class="btn btn-outline-light">
                                <em class="icon ni ni-arrow-left"></em>
                                <span><?php esc_html_e('Kurslarıma Dön', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <?php
                            // Tutor LMS'nin kurs oluşturma/düzenleme sayfasını yükle
                            if (function_exists('tutor_pro') && method_exists('tutor_pro', 'course_builder_template')) {
                                // Tutor Pro varsa, gelişmiş kurs oluşturucuyu kullan
                                ?>
                                <div id="tutor-course-builder-app">
                                    <?php
                                    // Tutor Pro course builder'ı yükle
                                    if ($is_edit_mode) {
                                        setup_postdata($post);
                                    }
                                    
                                    do_action('tutor_frontend_course_builder');
                                    
                                    if ($is_edit_mode) {
                                        wp_reset_postdata();
                                    }
                                    ?>
                                </div>
                                <?php
                            } else {
                                // Tutor Free için basit form
                                ?>
                                <div class="marketking-course-form">
                                    <?php
                                    if ($is_edit_mode) {
                                        // Düzenleme modu için mevcut kurs bilgilerini göster
                                        ?>
                                        <div class="alert alert-info">
                                            <h6><?php esc_html_e('Kurs Bilgileri', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                            <p><strong><?php esc_html_e('Başlık:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong> <?php echo esc_html($post->post_title); ?></p>
                                            <p><strong><?php esc_html_e('Durum:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong> 
                                                <?php 
                                                switch ($post->post_status) {
                                                    case 'publish':
                                                        esc_html_e('Yayınlandı', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                    case 'pending':
                                                        esc_html_e('Beklemede', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                    case 'draft':
                                                        esc_html_e('Taslak', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                    case 'future':
                                                        esc_html_e('Programlı', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                    default:
                                                        echo esc_html($post->post_status);
                                                        break;
                                                }
                                                ?>
                                            </p>
                                            <p><strong><?php esc_html_e('Oluşturulma Tarihi:', 'marketking-multivendor-marketplace-for-woocommerce'); ?></strong> <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($post->post_date))); ?></p>
                                        </div>
                                        
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $course_id . '&action=edit')); ?>" class="btn btn-primary btn-block" target="_blank">
                                                    <em class="icon ni ni-edit"></em>
                                                    <span><?php esc_html_e('WordPress Admin\'de Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                </a>
                                            </div>
                                            <div class="col-md-6">
                                                <a href="<?php echo esc_url(get_permalink($course_id)); ?>" class="btn btn-outline-primary btn-block" target="_blank">
                                                    <em class="icon ni ni-eye"></em>
                                                    <span><?php esc_html_e('Kursu Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                </a>
                                            </div>
                                        </div>
                                        <?php
                                    } else {
                                        // Yeni kurs oluşturma
                                        ?>
                                        <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" class="marketking-create-course-form">
                                            <?php wp_nonce_field('tutor_create_course_nonce', 'tutor_create_course_nonce_field'); ?>
                                            <input type="hidden" name="action" value="tutor_create_course">
                                            <input type="hidden" name="redirect_to" value="<?php echo esc_url($dashboard_url . 'my-courses'); ?>">
                                            
                                            <div class="row g-3">
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label class="form-label" for="course_title"><?php esc_html_e('Kurs Başlığı', 'marketking-multivendor-marketplace-for-woocommerce'); ?> <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" id="course_title" name="course_title" required 
                                                               placeholder="<?php esc_attr_e('Kursunuzun başlığını girin', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                    </div>
                                                </div>
                                                
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label class="form-label" for="course_description"><?php esc_html_e('Kurs Açıklaması', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                                        <textarea class="form-control" id="course_description" name="course_description" rows="4"
                                                                  placeholder="<?php esc_attr_e('Kursunuzun kısa açıklamasını girin', 'marketking-multivendor-marketplace-for-woocommerce'); ?>"></textarea>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label" for="course_category"><?php esc_html_e('Kategori', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                                        <select class="form-select" id="course_category" name="course_category">
                                                            <option value=""><?php esc_html_e('Kategori Seçin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                            <?php
                                                            $categories = get_terms(array(
                                                                'taxonomy' => 'course-category',
                                                                'hide_empty' => false,
                                                            ));
                                                            if (!is_wp_error($categories) && !empty($categories)) {
                                                                foreach ($categories as $category) {
                                                                    echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                                                }
                                                            }
                                                            ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label class="form-label" for="course_level"><?php esc_html_e('Seviye', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                                        <select class="form-select" id="course_level" name="course_level">
                                                            <option value=""><?php esc_html_e('Seviye Seçin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                            <option value="beginner"><?php esc_html_e('Başlangıç', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                            <option value="intermediate"><?php esc_html_e('Orta', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                            <option value="expert"><?php esc_html_e('İleri', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                                
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <button type="submit" class="btn btn-primary">
                                                            <em class="icon ni ni-plus"></em>
                                                            <span><?php esc_html_e('Kurs Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                        </button>
                                                        <a href="<?php echo esc_url($dashboard_url . 'my-courses'); ?>" class="btn btn-outline-secondary ml-2">
                                                            <span><?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                        
                                        <div class="alert alert-info mt-4">
                                            <h6><?php esc_html_e('Bilgi', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                            <p><?php esc_html_e('Kurs oluşturduktan sonra, kurs içeriğini eklemek ve düzenlemek için WordPress admin panelini kullanabilirsiniz. Tutor Pro ile daha gelişmiş kurs oluşturma araçlarına erişebilirsiniz.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                        </div>
                                        <?php
                                    }
                                    ?>
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.marketking-course-form .form-group {
    margin-bottom: 1.5rem;
}

.marketking-course-form .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.marketking-course-form .text-danger {
    color: #e85347 !important;
}

.btn-block {
    width: 100%;
}

#tutor-course-builder-app {
    min-height: 500px;
}
</style>

<?php
// Tutor LMS'nin gerekli script'lerini yükle
if (function_exists('tutor_utils')) {
    wp_enqueue_script('tutor-frontend');
    wp_enqueue_style('tutor-frontend');
}
?>
