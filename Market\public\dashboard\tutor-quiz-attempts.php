<?php
/**
 * Quiz Attempts Page for Market Dashboard
 * Adapted from Tutor LMS
 *
 * @package MarketKing
 * @subpackage Dashboard
 * <AUTHOR>
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Tutor LMS fonksiyonlarının mevcut olup olmadığını kontrol et
if (!function_exists('tutor') || !function_exists('tutor_utils')) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Tutor LMS eklentisi aktif değil. Sınav denemelerini görüntülemek için Tutor LMS eklentisini aktifleştirin.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Kullanıcının instructor olu<PERSON> o<PERSON>dığını kontrol et
if (!current_user_can(tutor()->instructor_role)) {
    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="alert alert-warning">
                        <?php esc_html_e('Sınav denemelerini görüntülemek için instructor yetkisine sahip olmanız gerekiyor.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

use TUTOR\Input;
use Tutor\Models\QuizModel;

$current_user_id = get_current_user_id();
if (marketking()->is_vendor_team_member()) {
    $current_user_id = marketking()->get_team_member_parent();
}

// Check if viewing single attempt details
if (isset($_GET['view_quiz_attempt_id'])) {
    $attempt_id = intval($_GET['view_quiz_attempt_id']);
    $attempt = QuizModel::get_quiz_attempt($attempt_id);

    if (!$attempt) {
        ?>
        <div class="nk-content">
            <div class="container-fluid">
                <div class="nk-content-inner">
                    <div class="nk-content-body">
                        <div class="alert alert-danger">
                            <?php esc_html_e('Sınav denemesi bulunamadı.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        </div>
                        <a href="<?php echo esc_url($dashboard_url . 'tutor-quiz-attempts'); ?>" class="btn btn-primary">
                            <?php esc_html_e('Geri Dön', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return;
    }

    // Check if user has permission to view this attempt
    $course = get_post($attempt->course_id);
    if (!$course || $course->post_author != $current_user_id) {
        ?>
        <div class="nk-content">
            <div class="container-fluid">
                <div class="nk-content-inner">
                    <div class="nk-content-body">
                        <div class="alert alert-danger">
                            <?php esc_html_e('Bu sınav denemesini görüntüleme yetkiniz yok.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        </div>
                        <a href="<?php echo esc_url($dashboard_url . 'tutor-quiz-attempts'); ?>" class="btn btn-primary">
                            <?php esc_html_e('Geri Dön', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
        return;
    }

    // Load single attempt details
    $student = get_userdata($attempt->user_id);
    $quiz = get_post($attempt->quiz_id);
    $attempt_date = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($attempt->attempt_started_at));

    // Calculate percentage
    $percentage = 0;
    if ($attempt->total_marks > 0) {
        $percentage = ($attempt->earned_marks / $attempt->total_marks) * 100;
    }

    // Get quiz answers
    $answers = QuizModel::get_quiz_answers_by_attempt_id($attempt_id);

    ?>
    <div class="nk-content">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block-head nk-block-head-sm">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title page-title"><?php esc_html_e('Sınav Deneme Detayları', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                <div class="nk-block-des text-soft">
                                    <p><?php printf(esc_html__('%s - %s', 'marketking-multivendor-marketplace-for-woocommerce'), esc_html($student->display_name), esc_html($quiz->post_title)); ?></p>
                                </div>
                            </div>
                            <div class="nk-block-head-content">
                                <a href="<?php echo esc_url($dashboard_url . 'tutor-quiz-attempts'); ?>" class="btn btn-outline-light">
                                    <em class="icon ni ni-arrow-left"></em>
                                    <span><?php esc_html_e('Geri Dön', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Attempt Summary -->
                    <div class="nk-block">
                        <div class="card card-bordered">
                            <div class="card-inner">
                                <div class="row g-4">
                                    <div class="col-md-3">
                                        <div class="nk-wg-stats">
                                            <div class="nk-wg-stats-info">
                                                <h6 class="title"><?php esc_html_e('Toplam Puan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                                <h4 class="amount"><?php echo esc_html($attempt->earned_marks . '/' . $attempt->total_marks); ?></h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="nk-wg-stats">
                                            <div class="nk-wg-stats-info">
                                                <h6 class="title"><?php esc_html_e('Yüzde', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                                <h4 class="amount"><?php echo esc_html(number_format($percentage, 1)); ?>%</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="nk-wg-stats">
                                            <div class="nk-wg-stats-info">
                                                <h6 class="title"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                                <h4 class="amount text-soft"><?php echo esc_html($attempt_date); ?></h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="nk-wg-stats">
                                            <div class="nk-wg-stats-info">
                                                <h6 class="title"><?php esc_html_e('Durum', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                                <h4 class="amount">
                                                    <?php
                                                    $pass_mark = tutor_utils()->get_quiz_option($attempt->quiz_id, 'passing_grade', 0);
                                                    if ($attempt->attempt_status === 'attempt_ended') {
                                                        if ($percentage >= $pass_mark) {
                                                            echo '<span class="text-success">' . esc_html__('Geçti', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                                        } else {
                                                            echo '<span class="text-danger">' . esc_html__('Kaldı', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                                        }
                                                    } else {
                                                        echo '<span class="text-warning">' . esc_html__('İnceleme Gerekli', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                                    }
                                                    ?>
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Student Info -->
                    <div class="nk-block">
                        <div class="card card-bordered">
                            <div class="card-inner">
                                <h6 class="card-title"><?php esc_html_e('Öğrenci Bilgileri', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('Ad Soyad', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-plaintext"><?php echo esc_html($student->display_name); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label"><?php esc_html_e('E-posta', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                            <div class="form-control-plaintext"><?php echo esc_html($student->user_email); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quiz Answers -->
                    <?php if ($answers) : ?>
                        <div class="nk-block">
                            <div class="card card-bordered">
                                <div class="card-inner">
                                    <h6 class="card-title"><?php esc_html_e('Sınav Cevapları', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h6>
                                    <div class="alert alert-info">
                                        <p><?php esc_html_e('Detaylı sınav cevaplarını ve puanlamayı WordPress admin panelinden görüntüleyebilirsiniz.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=quiz_attempts&sub_page=reviews&attempt_id=' . $attempt_id)); ?>" class="btn btn-primary" target="_blank">
                                            <em class="icon ni ni-external"></em>
                                            <span><?php esc_html_e('Admin Panelde Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

$item_per_page = tutor_utils()->get_option('pagination_per_page');
$current_page = max(1, Input::get('current_page', 1, Input::TYPE_INT));
$offset = ($current_page - 1) * $item_per_page;

// Filter params.
$course_filter = Input::get('course-id', '');
$order_filter = Input::get('order', 'DESC');
$date_filter = Input::get('date', '');

$course_id = tutor_utils()->get_assigned_courses_ids_by_instructors($current_user_id);
$quiz_attempts = QuizModel::get_quiz_attempts($offset, $item_per_page, '', $course_filter, $date_filter, $order_filter, null, false, true);
$quiz_attempts_count = QuizModel::get_quiz_attempts($offset, $item_per_page, '', $course_filter, $date_filter, $order_filter, null, true, true);

$dashboard_url = trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true)));

// Get instructor courses for filter
$instructor_courses = tutor_utils()->get_courses_for_instructors($current_user_id);
?>

<div class="nk-content">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h4 class="nk-block-title page-title"><?php esc_html_e('Sınav Denemeleri', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                            <div class="nk-block-des text-soft">
                                <p><?php esc_html_e('Öğrencilerinizin sınav denemelerini görüntüleyin ve değerlendirin', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label"><?php esc_html_e('Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                        <select class="form-select" name="course-id">
                                            <option value=""><?php esc_html_e('Tüm Kurslar', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                            <?php if ($instructor_courses) : ?>
                                                <?php foreach ($instructor_courses as $course) : ?>
                                                    <option value="<?php echo esc_attr($course->ID); ?>" <?php selected($course_filter, $course->ID); ?>>
                                                        <?php echo esc_html($course->post_title); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                        <input type="date" class="form-control" name="date" value="<?php echo esc_attr($date_filter); ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label"><?php esc_html_e('Sıralama', 'marketking-multivendor-marketplace-for-woocommerce'); ?></label>
                                        <select class="form-select" name="order">
                                            <option value="DESC" <?php selected($order_filter, 'DESC'); ?>><?php esc_html_e('Yeniden Eskiye', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                            <option value="ASC" <?php selected($order_filter, 'ASC'); ?>><?php esc_html_e('Eskiden Yeniye', 'marketking-multivendor-marketplace-for-woocommerce'); ?></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary d-block">
                                            <em class="icon ni ni-search"></em>
                                            <span><?php esc_html_e('Filtrele', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Quiz Attempts List -->
                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <?php if (is_array($quiz_attempts) && count($quiz_attempts)) : ?>
                                <div class="card-inner p-0">
                                    <div class="nk-tb-list nk-tb-ulist">
                                        <div class="nk-tb-item nk-tb-head">
                                            <div class="nk-tb-col"><span class="sub-text"><?php esc_html_e('Öğrenci', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Sınav', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-lg"><span class="sub-text"><?php esc_html_e('Kurs', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Puan', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-lg"><span class="sub-text"><?php esc_html_e('Tarih', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col tb-col-md"><span class="sub-text"><?php esc_html_e('Durum', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span></div>
                                            <div class="nk-tb-col nk-tb-col-tools text-right">
                                                <span class="sub-text"><?php esc_html_e('İşlemler', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                            </div>
                                        </div>
                                        
                                        <?php foreach ($quiz_attempts as $attempt) : ?>
                                            <?php
                                            $student = get_userdata($attempt->user_id);
                                            $quiz = get_post($attempt->quiz_id);
                                            $course = get_post($attempt->course_id);
                                            $attempt_date = date_i18n(get_option('date_format'), strtotime($attempt->attempt_started_at));
                                            
                                            // Calculate percentage
                                            $percentage = 0;
                                            if ($attempt->total_marks > 0) {
                                                $percentage = ($attempt->earned_marks / $attempt->total_marks) * 100;
                                            }
                                            
                                            // Determine status
                                            $status_class = '';
                                            $status_text = '';
                                            $pass_mark = tutor_utils()->get_quiz_option($attempt->quiz_id, 'passing_grade', 0);
                                            
                                            if ($attempt->attempt_status === 'attempt_ended') {
                                                if ($percentage >= $pass_mark) {
                                                    $status_class = 'badge-success';
                                                    $status_text = __('Geçti', 'marketking-multivendor-marketplace-for-woocommerce');
                                                } else {
                                                    $status_class = 'badge-danger';
                                                    $status_text = __('Kaldı', 'marketking-multivendor-marketplace-for-woocommerce');
                                                }
                                            } elseif ($attempt->attempt_status === 'review_required') {
                                                $status_class = 'badge-warning';
                                                $status_text = __('İnceleme Gerekli', 'marketking-multivendor-marketplace-for-woocommerce');
                                            } else {
                                                $status_class = 'badge-info';
                                                $status_text = __('Devam Ediyor', 'marketking-multivendor-marketplace-for-woocommerce');
                                            }
                                            ?>
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col">
                                                    <div class="user-card">
                                                        <div class="user-avatar bg-primary">
                                                            <span><?php echo esc_html(strtoupper(substr($student->display_name, 0, 2))); ?></span>
                                                        </div>
                                                        <div class="user-info">
                                                            <span class="tb-lead"><?php echo esc_html($student->display_name); ?></span>
                                                            <span class="fs-12px text-soft"><?php echo esc_html($student->user_email); ?></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col tb-col-md">
                                                    <span class="tb-amount"><?php echo esc_html($quiz ? $quiz->post_title : __('Bilinmiyor', 'marketking-multivendor-marketplace-for-woocommerce')); ?></span>
                                                </div>
                                                <div class="nk-tb-col tb-col-lg">
                                                    <span class="tb-status"><?php echo esc_html($course ? $course->post_title : __('Bilinmiyor', 'marketking-multivendor-marketplace-for-woocommerce')); ?></span>
                                                </div>
                                                <div class="nk-tb-col tb-col-md">
                                                    <span class="tb-amount">
                                                        <?php echo esc_html($attempt->earned_marks . '/' . $attempt->total_marks); ?>
                                                        <span class="currency">(<?php echo esc_html(number_format($percentage, 1)); ?>%)</span>
                                                    </span>
                                                </div>
                                                <div class="nk-tb-col tb-col-lg">
                                                    <span class="tb-date"><?php echo esc_html($attempt_date); ?></span>
                                                </div>
                                                <div class="nk-tb-col tb-col-md">
                                                    <span class="badge <?php echo esc_attr($status_class); ?>"><?php echo esc_html($status_text); ?></span>
                                                </div>
                                                <div class="nk-tb-col nk-tb-col-tools">
                                                    <ul class="nk-tb-actions gx-1">
                                                        <li>
                                                            <div class="drodown">
                                                                <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-toggle="dropdown">
                                                                    <em class="icon ni ni-more-h"></em>
                                                                </a>
                                                                <div class="dropdown-menu dropdown-menu-right">
                                                                    <ul class="link-list-opt no-bdr">
                                                                        <li><a href="<?php echo esc_url(add_query_arg('view_quiz_attempt_id', $attempt->attempt_id, $dashboard_url . 'tutor-quiz-attempts')); ?>">
                                                                            <em class="icon ni ni-eye"></em><span><?php esc_html_e('Detayları Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                        </a></li>
                                                                        <?php if ($attempt->attempt_status === 'review_required') : ?>
                                                                            <li><a href="<?php echo esc_url(admin_url('admin.php?page=quiz_attempts&sub_page=reviews&attempt_id=' . $attempt->attempt_id)); ?>" target="_blank">
                                                                                <em class="icon ni ni-edit"></em><span><?php esc_html_e('İncele ve Puanla', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                                                            </a></li>
                                                                        <?php endif; ?>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <!-- Pagination -->
                                <?php
                                if ($quiz_attempts_count > $item_per_page) {
                                    $total_pages = ceil($quiz_attempts_count / $item_per_page);
                                    if ($total_pages > 1) {
                                        ?>
                                        <div class="card-inner">
                                            <div class="nk-block-between-md g-3">
                                                <div class="g">
                                                    <ul class="pagination justify-content-center justify-content-md-start">
                                                        <?php
                                                        $current_url = $dashboard_url . 'tutor-quiz-attempts';
                                                        $query_args = array();
                                                        if ($course_filter) $query_args['course-id'] = $course_filter;
                                                        if ($date_filter) $query_args['date'] = $date_filter;
                                                        if ($order_filter !== 'DESC') $query_args['order'] = $order_filter;
                                                        
                                                        // Previous page
                                                        if ($current_page > 1) {
                                                            $prev_page = $current_page - 1;
                                                            $prev_args = $query_args;
                                                            if ($prev_page > 1) $prev_args['current_page'] = $prev_page;
                                                            $prev_url = add_query_arg($prev_args, $current_url);
                                                            ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="<?php echo esc_url($prev_url); ?>">
                                                                    <em class="icon ni ni-chevrons-left"></em>
                                                                </a>
                                                            </li>
                                                            <?php
                                                        }
                                                        
                                                        // Page numbers
                                                        for ($i = 1; $i <= $total_pages; $i++) {
                                                            $page_args = $query_args;
                                                            if ($i > 1) $page_args['current_page'] = $i;
                                                            $page_url = add_query_arg($page_args, $current_url);
                                                            ?>
                                                            <li class="page-item<?php echo $i == $current_page ? ' active' : ''; ?>">
                                                                <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo esc_html($i); ?></a>
                                                            </li>
                                                            <?php
                                                        }
                                                        
                                                        // Next page
                                                        if ($current_page < $total_pages) {
                                                            $next_page = $current_page + 1;
                                                            $next_args = $query_args;
                                                            $next_args['current_page'] = $next_page;
                                                            $next_url = add_query_arg($next_args, $current_url);
                                                            ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="<?php echo esc_url($next_url); ?>">
                                                                    <em class="icon ni ni-chevrons-right"></em>
                                                                </a>
                                                            </li>
                                                            <?php
                                                        }
                                                        ?>
                                                    </ul>
                                                </div>
                                                <div class="g">
                                                    <div class="pagination-goto d-flex justify-content-center justify-content-md-end gx-3">
                                                        <div><?php printf(esc_html__('Sayfa %1$d / %2$d', 'marketking-multivendor-marketplace-for-woocommerce'), $current_page, $total_pages); ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>
                            <?php else : ?>
                                <div class="card-inner text-center">
                                    <div class="nk-empty-state">
                                        <div class="nk-empty-state-icon">
                                            <em class="icon ni ni-file-check"></em>
                                        </div>
                                        <h4 class="nk-empty-state-title"><?php esc_html_e('Henüz sınav denemesi bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p class="nk-empty-state-text"><?php esc_html_e('Öğrencileriniz sınavlara girmeye başladığında burada görüntülenecek.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                        <div class="nk-empty-state-action">
                                            <a href="<?php echo esc_url($dashboard_url . 'my-courses'); ?>" class="btn btn-primary">
                                                <em class="icon ni ni-book-read"></em>
                                                <span><?php esc_html_e('Kurslarıma Git', 'marketking-multivendor-marketplace-for-woocommerce'); ?></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 12px;
}

.user-card {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.tb-lead {
    font-weight: 600;
    color: #364a63;
}

.fs-12px {
    font-size: 12px;
}

.text-soft {
    color: #8094ae;
}
</style>
